# SGS-2689: Casino Table Provider Data Integrity Issue

## Jira Ticket Information
- **Ticket**: [SGS-2689](https://saminogaming.atlassian.net/browse/SGS-2689)
- **Title**: Data integrity issue: Casino Table provider field saves wrong value in DB
- **Type**: Bug
- **Priority**: Medium
- **Status**: In Progress
- **Assignee**: Prayas
- **Created**: 2025-07-30
- **Updated**: 2025-07-30

## Issue Summary
When creating new Casino Table records in Super Admin > Game Management > All Tables, the wrong provider is being saved to the database, causing data mismatch that affects game seeding functionality.

## Environment
- **Affected**: Development and Production
- **Impact**: Game seeding functionality compromised

## Task Breakdown

### [ ] 1. Investigation Phase
- [ ] Analyze current Casino Table creation flow in Super Admin
- [ ] Review Game Management > All Tables component code
- [ ] Identify provider selection logic and data binding
- [ ] Check database schema for Casino Table provider field
- [ ] Review API endpoints for Casino Table creation
- [ ] Examine form validation and data transformation

### [ ] 2. Root Cause Analysis
- [ ] Identify where provider mismatch occurs (Frontend/Backend/Database)
- [ ] Check if issue is in form data binding
- [ ] Verify API request payload vs database insertion
- [ ] Review provider dropdown population logic
- [ ] Check for any data transformation issues

### [ ] 3. Code Review & Fix
- [ ] Locate the specific component handling Casino Table creation
- [ ] Review provider field mapping in form submission
- [ ] Fix the provider data binding issue
- [ ] Ensure proper validation of provider selection
- [ ] Update any related service methods

### [ ] 4. Testing
- [ ] Test Casino Table creation with different providers
- [ ] Verify correct provider is saved to database
- [ ] Test in both Development and Production environments
- [ ] Validate game seeding functionality works correctly
- [ ] Test edge cases and error scenarios

### [ ] 5. Documentation & Deployment
- [ ] Document the fix and root cause
- [ ] Update any relevant technical documentation
- [ ] Create deployment notes if needed
- [ ] Update Jira ticket with resolution details

## Technical Notes

### Components to Investigate
- Super Admin Casino Table creation form
- Game Management module
- Provider selection dropdown component
- Casino Table service/API layer
- Database model for Casino Table

### Key Areas to Check
1. **Frontend Form Binding**
   - Provider dropdown selection
   - Form data collection
   - Data validation before submission

2. **API Layer**
   - Request payload structure
   - Provider field mapping
   - Data transformation logic

3. **Backend Processing**
   - Database insertion logic
   - Provider field validation
   - Data integrity checks

4. **Database Schema**
   - Casino Table provider field definition
   - Foreign key relationships
   - Data constraints

## Expected Outcome
- Provider selection in UI correctly saves to database
- Game seeding functionality restored
- Data integrity maintained across environments
- No regression in existing Casino Table functionality

## Time Estimate
- Investigation: 2-3 hours
- Fix Implementation: 2-4 hours
- Testing: 2-3 hours
- **Total**: 6-10 hours

## Dependencies
- Access to Super Admin panel
- Database access for verification
- Game seeding test environment

## Risk Assessment
- **Low Risk**: Isolated to Casino Table provider field
- **Medium Impact**: Affects game seeding functionality
- **High Priority**: Data integrity issue in production

---
**Created**: 2025-07-30  
**Last Updated**: 2025-07-30  
**Status**: Planning Phase
