Update: Simple Disposable Email Check (No Complex Package Required)

Instead of adding a heavy or complex package for blocking disposable emails, we've implemented a lightweight solution using a simple blocklist.

We added a check to block disposable email domains during email validation.

The list of disposable domains is loaded from a plain .conf file.

This approach avoids unnecessary dependencies and keeps things simple and maintainable.

Here’s a quick look at the implementation:

js
Copy
Edit
let blocklist;

async function isDisposable(email) {
  if (!blocklist) {
    const content = await readFile('disposable_email_blocklist.conf', { encoding: 'utf-8' });
    blocklist = content.split('\r\n').filter(Boolean); // removes empty lines
  }

  const domain = email.split('@')[1];
  return blocklist.includes(domain);
}
We're using the blocklist from:

📄 disposable_email_blocklist.conf

🔗 Project README

This is a simple and effective way to block signups using throwaway/disposable emails.

Let me know if you have any feedback or suggestions.